{"compilerOptions": {"target": "ES5", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "declaration": false, "outDir": "./Scripts/compiled", "rootDir": "./<PERSON><PERSON><PERSON>"}, "include": ["Scripts/**/*"], "exclude": ["node_modules", "Scripts/compiled"]}